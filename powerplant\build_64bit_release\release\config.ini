; Configuration file for RS485 application
; Generated at Jun 12 2025 09:35:09

[ProtocolList]
list = RS485,DCS_RS485

[RS485]
Port = COM5
BaudRate = 9600
StopBits = 2
Parity = N
DataBits = 8
Timeout = 1.0

[DCS_RS485]
Port = COM6
BaudRate = 9600
StopBits = 1
Parity = N
DataBits = 8
Timeout = 2.0

;Boiler name list
[BoilerList]
list = #3机组

[#3机组]
Desc = this is aboiler1
Protocol = RS485
CollectionInterval = 3
Nox = 20
Current = 60
So2 = 30
Voltage = 50
O2 = 4
Co = 1
Tempature = 40


;DCS设备列表
[DCSList]
list = DCS1

[DCS1]
Desc = 分布式控制系统1
Protocol = DCS_RS485
CollectionInterval = 10
DeviceAddress = 12
StartRegister = 31
RegisterCount = 44
AssociatedBoiler = #3机组
; 数据映射配置 - 基于88字节数据的字节偏移量
; 报文格式：7字节头部 + 88字节数据，数据从第8字节开始
FurnacePressureOffset = 0
SuperheaterTempOffset = 4
GeneratorPowerOffset = 8
MainSteamPressureOffset = 12
TotalAirFlowOffset = 16
WaterCoalRatioOffset = 24
PrimaryFanAOffset = 28
PrimaryFanBOffset = 32
FanAOffset = 36
FanBOffset = 40
InducedFanAOffset = 44
InducedFanBOffset = 48
COOffset = 52
O2Offset = 56
SO2Offset = 60
NOxOffset = 64




[#3机组]
Switch1 = 80
BackflowDelayTime = 60

[DCS_RS485]
O2Offset = 56
FanBOffset = 40
FanAOffset = 36
NOxOffset = 64
PrimaryFanBOffset = 32
SO2Offset = 60
PrimaryFanAOffset = 28
WaterCoalRatioOffset = 24
TotalAirFlowOffset = 16
MainSteamPressureOffset = 12
COOffset = 52
InducedFanAOffset = 44
GeneratorPowerOffset = 8
CollectionInterval = 10
FurnacePressureOffset = 0
DeviceAddress = 12
StartRegister = 31
InducedFanBOffset = 48
RegisterCount = 44
SuperheaterTempOffset = 4
